
const permssionRoutes = [
    {
        path: 'chat',
        name: 'chat',
        component: () => import('@/windows/main/chat/index.vue'),
        meta: {
            // 窗口不能最小化
            minimizable: true,
            // 窗口不能进入全屏状态
            fullscreenable: true,
            // 窗口不能关闭
            closeable: true,
            // 权限标识
            permissionKey: 'chat',
            icon: 'chat',
            title: '聊天管理'
        }
    },
    {
        path: 'tran-text',
        name: 'tranText',
        component: () => import('@/windows/main/tranText/index.vue'),
        meta: {
            // 窗口不能最小化
            minimizable: true,
            // 窗口不能进入全屏状态
            fullscreenable: true,
            // 窗口不能关闭
            closeable: true,
            // 权限标识
            permissionKey: 'tranText',
            icon: 'tran-text',
            title: '文本翻译'
        }
    },
    {
        path: 'tran-image',
        name: 'tranImage',
        component: () => import('@/windows/main/tranImages/index.vue'),
        meta: {
            // 窗口不能最小化
            minimizable: true,
            // 窗口不能进入全屏状态
            fullscreenable: true,
            // 窗口不能关闭
            closeable: true,
            // 权限标识
            permissionKey: 'tranImage',
            icon: 'tran-image',
            title: '图片翻译'
        }
    },
    {
        path: 'tran-doc',
        name: 'tranDoc',
        component: () => import('@/windows/main/tranDoc/index.vue'),
        meta: {
            // 窗口不能最小化
            minimizable: true,
            // 窗口不能进入全屏状态
            fullscreenable: true,
            // 窗口不能关闭
            closeable: true,
            // 权限标识
            permissionKey: 'tranDoc',
            icon: 'tran-doc',
            title: '文档翻译'
        }
    },
];

export { permssionRoutes };