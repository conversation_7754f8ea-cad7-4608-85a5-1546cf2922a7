import { autoUpdater } from "electron-updater";
import { app } from "electron";

export function initAutoUpdater() {
    console.log(app.getVersion());

    // 检查更新的相对路径
    const updateUrl = 'https://electron-app-versions.oss-cn-shenzhen.aliyuncs.com/test';

    autoUpdater.setFeedURL(`${updateUrl}`);

    autoUpdater.on('checking-for-update', () => {
        console.log('正在检查更新...');
    });

    autoUpdater.on('update-available', (info) => {
        console.log('发现新版本', info);
    });

    autoUpdater.on('update-not-available', (info) => {
        console.log('现在使用的是最新版本', info);
    });

    autoUpdater.on('download-progress', (progress) => {
        console.log('下载进度', progress);
    });

    autoUpdater.on('error', (err) => {
        console.error('更新时发生错误:', err);
    });

    autoUpdater.on('update-downloaded', (info) => {
        console.log('更新下载完成', info);
        autoUpdater.quitAndInstall();
    });
}

export function checkForUpdatesAndNotify() {
    autoUpdater.checkForUpdatesAndNotify();
}