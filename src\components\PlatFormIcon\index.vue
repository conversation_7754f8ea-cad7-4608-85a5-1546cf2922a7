<template>
    <SvgIcon v-bind="$attrs" :name="name"></SvgIcon>
</template>
<script setup lang="ts">
import SvgIcon from '@/components/SvgIcon/index.vue'
import { computed } from 'vue';
import { PlatformKeys } from '../../constants/enum';
const props = defineProps<{
    platform: PlatformKeys
}>()
const name = computed(() => {
    switch (props.platform) {
        case PlatformKeys.WhatsApp:
            return 'whatsapp';
        case PlatformKeys.Telegram:
            return 'telegram';
        case PlatformKeys.LINE:
            return 'line';
    }
})
</script>