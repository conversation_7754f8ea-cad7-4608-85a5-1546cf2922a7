import { Rectangle } from "electron";

export interface SessionWindowsManagerOptions {
    id: string,
    url: string,
    preload: string,
    bounds: Rectangle;
    proxyConfig?: ProxyConfig;
}

export interface IAccountInfo {
    avatar?: string;
    accountId?: string;
    nickname?: string;
    isLogined: boolean;
    unreadCount: number;
}

export interface IChatWindowInfo {
    winId: string,
    isLoading: boolean;
    isLoadFail: boolean;
    isLoaded: boolean;
    isShow: boolean;
    isCreated: boolean;
    accountInfo: IAccountInfo
}

export interface TranslateConfig {
    // 聊天记录翻译
    translateChatRecord: boolean
    chatRecordTranslateLine: string
    chatRecordTranslateSource: string
    chatRecordTranslateTarget: string

    // 输入框翻译
    translateInput: boolean
    inputTranslateLine: string
    inputTranslateSource: string
    inputTranslateTarget: string

    // 图片翻译
    translateImage: boolean
    imageTranslateLine: string
    imageTranslateSource: string
    imageTranslateTarget: string

    // 语音翻译
    translateAudio: boolean
    audioTranslateLine: string
    audioTranslateSource: string
    audioTranslateTarget: string

}

export interface ProxyConfig {
    enabled: boolean,
    protocol: string,
    host: string,
    port: string,
    auth: boolean,
    username: string,
    password: string
}

export interface FingerprintConfig {
    enabled: boolean
    os: string
    browser: string
    resolution: string
    language: string
    timezone: string
    userAgent: string
    cookie: string
    webrtc: boolean
    canvas: boolean
    webgl: boolean
}

export interface TranslateTextOptions {
    from: string
    to: string
    vendor: string
}