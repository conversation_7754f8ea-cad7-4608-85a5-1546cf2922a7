// Pinia Store
import { defineStore } from 'pinia'
import { person } from '../api/user'
import { $message } from '../utils/message'
import { permssionRoutes } from '../router/perm'
import { IPlatform, IUserInfo } from '@/types';

interface IState {
    userInfo: null | IUserInfo;
    platforms: Array<IPlatform>;
    permissions: string[]
};

export const useUserInfoStore = defineStore('user/info', {
    // 转换为函数
    state: (): IState => ({
        userInfo: null,
        platforms: [
            {
                name: 'WhatsApp',
                key: 'whatsapp',
                state: 'normal',
                icon: 'whatsapp',
                color: '#1daa61',
                activeColor: '#1daa61',
            },
            {
                name: 'Telegram',
                key: 'telegram',
                state: 'normal',
                icon: 'telegram',
                color: '#328feb',
                activeColor: '#328feb',
            },
            {
                name: 'LINE',
                key: 'line',
                state: 'normal',
                icon: 'line',
                color: '#07b53b',
                activeColor: '#07b53b',
            }
        ],
        permissions: []
    }),
    getters: {

    },
    actions: {
        getUserInfo(): Promise<IUserInfo> {
            return person().then((data: any) => {
                this.userInfo = data;
                this.permissions = permssionRoutes.map(i => i.meta?.permissionKey)
                return data;
            }).catch(err => {
                $message.error('获取用户信息失败');
            });
        }
    },
})