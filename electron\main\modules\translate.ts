import { TranslateTextOptions } from "../interface";
import { translateAPI } from '../api/translate'

export async function translateText(text: string, translateOptions: TranslateTextOptions) {
    try {
        if (!translateOptions.from || !translateOptions.to || !translateOptions.vendor) {
            return "请检查翻译配置"
        }
        const res = await translateAPI({
            text,
            ...translateOptions,
        })
        return res.data.text
    } catch (e) {
        console.log(e);
        return '翻译出错，请稍后再试'
    }
}