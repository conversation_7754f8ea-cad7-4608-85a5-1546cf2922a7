export enum IEventType {
    QuitApp = 'quitApp',
    ToLoginWindow = 'toLoginWindow',
    ToMainWindow = 'toMainWindow',
    ToUpdateWindow = 'toUpdateWindow',
    CreateChatWindow = 'createChatWindow',
    SetChatWindowBounds = 'setChatWindowBounds',
    WinMinimize = 'winMinimize',
    WinClose = 'winClose',
    WinMaximize = 'winMaximize',
    ShowChatWindow = 'showChatWindow',
    HideChatWindow = 'hideChatWindow',
    CloseChatWindow = 'closeChatWindow',
    GetChatWindowInfo = 'getChatWindowInfo',
    GetChatWindowInfoList = 'getChatWindowInfoList',
    ReloadChatWindow = 'reloadChatWindow',
    SetTranslateConfig = 'set-translate-config',
    GetTranslateConfig = 'get-translate-config',
    SetProxyConfig = 'set-proxy-config',
    GetProxyConfig = 'get-proxy-config',
    SetFingerprintConfig = 'set-fingerprint-config',
    GetFingerprintConfig = 'get-fingerprint-config',
    TranslateText = 'translate-text',
    GetToken = 'get-token',
    SetToken = 'set-token',
    RemoveToken = 'remove-token',
}

export enum IEmitType {
    ChatWindowEmit = 'chatWindowEmit',
    OnChatWindowCreated = 'onChatWindowCreated',
    OnChatWindowShow = 'onChatWindowShow',
    OnChatWindowHide = 'onChatWindowHide',
    OnChatWindowClosed = 'onChatWindowClosed',
    OnChatWindowResized = 'onChatWindowResized',
    OnChatWindowReadyed = 'onChatWindowReadyed',
    OnChatWindowLoadFail = 'onChatWindowLoadFail',
    OnChatWindowStartLoading = 'onChatWindowStartLoading',
    OnPlatformAccountChange = 'onPlatformAccountChange'
}

export enum ICoreWindowId {
    Main = 'main',
    Login = 'login',
    Update = 'update',
    Launch = 'launch'
}

export enum PlatformKeys {
    WhatsApp = 'whatsapp',
    Telegram = 'telegram',
    LINE = 'line',
}