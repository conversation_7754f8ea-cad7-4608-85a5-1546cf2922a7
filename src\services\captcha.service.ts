import svgCaptcha from 'svg-captcha';
import svgToDataURL from 'svg-to-dataurl';
import { RedisService } from './redis.service';

export class CaptchaService {
  private redisService: RedisService;
  private readonly CAPTCHA_PREFIX = 'captcha:';
  private readonly CAPTCHA_EXPIRE_TIME = 300; // 5分钟过期

  constructor() {
    this.redisService = new RedisService();
  }

  /**
   * 生成验证码
   */
  async generateCaptcha(): Promise<{ id: string; dataUrl: string }> {
    const captcha = svgCaptcha.create({
      ignoreChars: 'qwertyuiopasdfghjklzxcvbnmQWERTYUIOPASDFGHJKLZXCVBNM',
      size: 4,
      noise: 2,
      inverse: false,
      color: false,
    });

    // 生成唯一ID
    const id = Math.random().toString(36).substring(2, 15);

    // 将验证码答案存储到Redis
    await this.redisService.set(
      `${this.CAPTCHA_PREFIX}${id}`,
      captcha.text.toLowerCase(),
      this.CAPTCHA_EXPIRE_TIME,
    );

    // 将SVG转换为DataURL
    const dataUrl = await svgToDataURL(captcha.data);

    return { id, dataUrl };
  }

  /**
   * 验证验证码
   */
  async verifyCaptcha(id: string, answer: string): Promise<boolean> {
    const key = `${this.CAPTCHA_PREFIX}${id}`;
    const correctAnswer = await this.redisService.get(key);

    if (!correctAnswer) {
      return false;
    }

    // 验证完成后删除验证码
    await this.redisService.del(key);

    return correctAnswer.toLowerCase() === answer.toLowerCase();
  }
}
