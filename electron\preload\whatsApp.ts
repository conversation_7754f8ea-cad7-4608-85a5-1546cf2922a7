import { contextBridge, ipcRenderer } from 'electron'
import { onLocalStorageItemChange } from './utils/localStorageTools'
import { domReady } from './utils/domTools';
import { CommonBridge } from './common'

// 定义Bridge接口
interface IBridge {
    getUserId(): string | null;
    getUserAvatar(): string | null;
    getUserNickName(): string | null;
    getUnreadCount(): number;
    getLoginState(): boolean;
    messageObserver: MutationObserver | null
}

// --------- Expose some API to the Renderer process ---------
// 实现Bridge类
class Bridge extends CommonBridge implements IBridge {
    constructor() {
        super();
        // 确保方法绑定到实例
        this.getUserId = this.getUserId.bind(this);
        this.getUserAvatar = this.getUserAvatar.bind(this);
        this.getUserNickName = this.getUserNickName.bind(this);
        this.getUnreadCount = this.getUnreadCount.bind(this);
        this.getLoginState = this.getLoginState.bind(this);
        this.listenLocalStorageItemChange();
        this.listenChatMessageChange();
        domReady().then(async () => {
            console.log('DOM ready');
            ipcRenderer.send('account-login', { state: false });
            ipcRenderer.on('translate-config-changed', () => {
                this.setTranslateState();
            });
            this.setTranslateState();
            this.setupTranslateInput();
        });
    }

    messageObserver = null;

    private async setupTranslateInput() {
        this.initTranslateInput('#main-msg-input');
    }

    private listenLocalStorageItemChange() {
        const targetKey = 'whatsapp-account';
        onLocalStorageItemChange(targetKey, (value: string) => {
            try {
                const data = JSON.parse(value);
                ipcRenderer.send('account-login', { state: !!data.userId })
                ipcRenderer.send('localStorage-changed');
            } catch (e) {
                console.log('send account-login error:', e);
            }
        })
    }

    private listenChatMessageChange() {
        this.messageObserver = this.setupMessageObserver('#pane-side', () => {
            this.initTranslateChatItems('.message-in, .message-out', '.copyable-text');
        });
    }

    public getUserId(): string | null {
        try {
            const account = localStorage.getItem('whatsapp-account');
            if (!account) return null;
            const data = JSON.parse(account);
            return data.userId || null;
        } catch {
            return null;
        }
    }

    public getUserAvatar(): string | null {
        try {
            const account = localStorage.getItem('whatsapp-account');
            if (!account) return null;
            const data = JSON.parse(account);
            return data.avatar || null;
        } catch {
            return null;
        }
    }

    public getUserNickName(): string | null {
        try {
            const account = localStorage.getItem('whatsapp-account');
            if (!account) return null;
            const data = JSON.parse(account);
            return data.name || null;
        } catch {
            return null;
        }
    }

    public getUnreadCount(): number {
        try {
            const badges = document.querySelectorAll('span[aria-label*="unread"]');
            return Array.from(badges).reduce((sum, badge) => {
                const text = badge.textContent?.trim() || '0';
                return sum + parseInt(text, 10);
            }, 0);
        } catch {
            return 0;
        }
    }

    public getLoginState(): boolean {
        try {
            const account = localStorage.getItem('whatsapp-account');
            return !!account && !!JSON.parse(account).userId;
        } catch {
            return false;
        }
    }
}

// 暴露API到渲染进程
contextBridge.exposeInMainWorld('JSBridge', new Bridge());
