import { app, <PERSON><PERSON>erWindow, Menu } from 'electron'
import { release } from 'node:os'
import { registerGlobalShortcut } from './modules/globalShortcut';
import { createTray } from './modules/tray'
import { eventListener } from './modules/eventListener'
import { initAutoUpdater, checkForUpdatesAndNotify } from './modules/update';
import { coreWindowsManager } from './windows/index'

// Disable GPU Acceleration for Windows 7
if (release().startsWith('6.1')) app.disableHardwareAcceleration()

// Set application name for Windows 10+ notifications
if (process.platform === 'win32') app.setAppUserModelId(app.getName())

if (!app.requestSingleInstanceLock()) {
  app.quit()
  process.exit(0)
}

// Remove electron security warnings
// This warning only shows in development mode
// Read more on https://www.electronjs.org/docs/latest/tutorial/security
// process.env['ELECTRON_DISABLE_SECURITY_WARNINGS'] = 'true'
Menu.setApplicationMenu(null);
const createLaunchWindow = () => {
  coreWindowsManager.createLaunchWindow();
}
app.whenReady()
  .then(registerGlobalShortcut)
  .then(createTray)
  .then(createLaunchWindow)
  .then(eventListener)
  .then(() => {
    // initAutoUpdater();
    // checkForUpdatesAndNotify();
  })

app.on('window-all-closed', () => {
  // if (process.platform !== 'darwin') app.quit()
})

app.on('activate', () => {
  const allWindows = BrowserWindow.getAllWindows()
  if (allWindows.length) {
    allWindows[0].focus()
  } else {
    createLaunchWindow()
  }
})

app.on('ready', async () => {

});

