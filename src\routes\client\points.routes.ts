import Router from 'koa-router';
import { PointsController } from '../../controllers/client/points.controller';
import { tenantMiddleware } from '../../middleware/tenant.middleware';
import { clientAuthMiddleware } from '../../middleware/auth.middleware';

const router = new Router();
const pointsController = new PointsController();

// 应用中间件
router.use('/api/client/points', tenantMiddleware, clientAuthMiddleware);

/**
 * @route GET /api/client/points/products
 * @desc 获取积分商品列表
 * @access Private
 */
router.get('/api/client/points/products', pointsController.getProducts.bind(pointsController));

/**
 * @route GET /api/client/points/balance
 * @desc 获取用户积分余额
 * @access Private
 */
router.get('/api/client/points/balance', pointsController.getBalance.bind(pointsController));

/**
 * @route GET /api/client/points/transactions
 * @desc 获取积分流水记录
 * @access Private
 */
router.get(
  '/api/client/points/transactions',
  pointsController.getTransactions.bind(pointsController),
);

/**
 * @route POST /api/client/points/orders
 * @desc 创建积分订单
 * @access Private
 */
router.post('/api/client/points/orders', pointsController.createOrder.bind(pointsController));

/**
 * @route GET /api/client/points/orders
 * @desc 获取用户订单列表
 * @access Private
 */
router.get('/api/client/points/orders', pointsController.getOrders.bind(pointsController));

/**
 * @route POST /api/client/points/payment
 * @desc 创建支付链接
 * @access Private
 */
router.post('/api/client/points/payment', pointsController.createPayment.bind(pointsController));

/**
 * @route POST /api/client/points/payment/notify
 * @desc 支付回调通知
 * @access Public (zpay回调)
 */
router.post(
  '/api/client/points/payment/notify',
  pointsController.paymentNotify.bind(pointsController),
);

/**
 * @route PUT /api/client/points/orders/:orderNo/cancel
 * @desc 取消订单
 * @access Private
 */
router.put(
  '/api/client/points/orders/:orderNo/cancel',
  pointsController.cancelOrder.bind(pointsController),
);

export default router;
