export enum IEventType {
    QuitApp = 'quitApp',
    ToLoginWindow = 'toLoginWindow',
    ToMainWindow = 'toMainWindow',
    ToUpdateWindow = 'toUpdateWindow',
    CreateChatWindow = 'createChatWindow',
    SetChatWindowBounds = 'setChatWindowBounds',
    WinMinimize = 'winMinimize',
    WinClose = 'winClose',
    WinMaximize = 'winMaximize',
    ShowChatWindow = 'showChatWindow',
    HideChatWindow = 'hideChatWindow',
    CloseChatWindow = 'closeChatWindow',
    GetChatWindowInfo = 'getChatWindowInfo',
    GetChatWindowInfoList = 'getChatWindowInfoList',
    ReloadChatWindow = 'reloadChatWindow'
}

export enum IEmitType {
    ChatWindowEmit = 'chatWindowEmit',
    OnChatWindowCreated = 'onChatWindowCreated',
    OnChatWindowShow = 'onChatWindowShow',
    OnChatWindowHide = 'onChatWindowHide',
    OnChatWindowClosed = 'onChatWindowClosed',
    OnChatWindowResized = 'onChatWindowResized',
    OnChatWindowReadyed = 'onChatWindowReadyed',
    OnChatWindowLoadFail = 'onChatWindowLoadFail',
    OnChatWindowStartLoading = 'onChatWindowStartLoading',
    OnPlatformAccountChange = 'onPlatformAccountChange'
}

export enum PlatformKeys {
    WhatsApp = 'whatsapp',
    Telegram = 'telegram',
    LINE = 'line',
}