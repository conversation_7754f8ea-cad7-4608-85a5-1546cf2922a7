<template>
    <div class="box-border w-full h-full p-[30px_50px]">
        <div class="w-full h-full flex flex-col items-center">
            <div>
                <h2 class="m-0">有新版本v2.3.0</h2>
            </div>
            <div class="flex-grow w-full overflow-y-auto py-[15px]">
                <el-scrollbar>
                    <p v-for="item in 20" :key="item">{{ item }}</p>
                </el-scrollbar>
            </div>
            <div class="flex justify-center items-center">
                <div class="flex-grow flex-shrink-0 mr-[15px]">
                    <el-button block @click="quitApp()">暂不更新</el-button>
                </div>
                <div class="flex-grow flex-shrink-0">
                    <el-button type="primary" block>
                        立即更新
                    </el-button>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { quitApp } from '../../utils/windows'
</script>