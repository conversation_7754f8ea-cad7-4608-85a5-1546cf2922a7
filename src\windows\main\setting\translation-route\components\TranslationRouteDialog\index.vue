<template>
    <el-dialog v-model="visible" width="500px">
        <template #header>
            <div class="font-bold text-base">{{ isEdit ? '编辑线路' : '添加线路' }}</div>
        </template>
        <el-form ref="formRef" :model="form" :rules="rules" label-width="auto">
            <el-form-item label="供应商" prop="vendor">
                <el-select v-model="form.vendor" placeholder="请选择供应商">
                    <el-option v-for="item in vendorOptions" :key="item.type" :label="item.name"
                        :value="item.type"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="APIKEY" prop="apiKey">
                <el-input v-model="form.apiKey" placeholder="请输入APIKEY" />
            </el-form-item>
            <el-form-item label="APISECRET" prop="apiSecret">
                <el-input v-model="form.apiSecret" placeholder="请输入APISECRET" />
            </el-form-item>
            <el-form-item label="排序权重" prop="sort">
                <el-input-number v-model="form.sort" :min="0" :max="999" />
            </el-form-item>
            <el-form-item label="是否启用" prop="isActive">
                <el-switch v-model="form.isActive" />
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button type="primary" @click="handleSubmit" :loading="loading">确定</el-button>
            <el-button @click="handleCancel">取消</el-button>
        </template>
    </el-dialog>
</template>
<script lang="ts" setup>
import { ref, reactive } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { $message } from '@/utils/message'
import { createTranslationRouteAPI, updateTranslationRouteAPI, type ITranslationRoute, TranslationType, VendorType, getVendorsAPI, type IVendorInfo } from '@/api/translation-route'

const vendorOptions = ref<IVendorInfo[]>([])

interface IForm {
    type: TranslationType
    vendor: VendorType
    apiKey: string
    apiSecret: string
    sort: number
    isActive: boolean
    accountId: string
}

const visible = ref(false)
const loading = ref(false)
const formRef = ref<FormInstance>()
const isEdit = ref(false)
const currentId = ref('')

const form = reactive<IForm>({
    type: TranslationType.TEXT,
    vendor: VendorType.BAIDU,
    apiKey: '',
    apiSecret: '',
    sort: 0,
    isActive: true,
    accountId: '' // 需要从外部传入
})

const rules = reactive<FormRules>({
    vendor: [
        { required: true, message: '请选择供应商', trigger: 'change' }
    ],
    apiKey: [
        { required: true, message: '请输入APIKEY', trigger: 'blur' },
        { min: 5, message: 'APIKEY长度不能小于5个字符', trigger: 'blur' }
    ],
    apiSecret: [
        { min: 5, message: 'APISECRET长度不能小于5个字符', trigger: 'blur' }
    ],
    sort: [
        { required: true, message: '请输入排序权重', trigger: 'blur' }
    ]
})

const open = async (data?: ITranslationRoute, accountId?: string, type?: TranslationType) => {
    visible.value = true

    try {
        const res = await getVendorsAPI()
        vendorOptions.value = res.data
    } catch (error) {
        console.error('获取供应商列表失败:', error)
        $message.error('获取供应商列表失败')
    }

    if (data) {
        isEdit.value = true
        currentId.value = data.id
        form.type = data.type
        form.vendor = data.vendor
        form.apiKey = data.apiKey
        form.apiSecret = data.apiSecret || ''
        form.sort = data.sort
        form.isActive = data.isActive
        form.accountId = data.accountId
    } else {
        isEdit.value = false
        currentId.value = ''
        form.type = type || TranslationType.TEXT
        form.accountId = accountId || ''
        formRef.value?.resetFields()
    }
}

const handleSubmit = async () => {
    if (!formRef.value) return

    try {
        await formRef.value.validate()
        loading.value = true

        const params = {
            type: form.type,
            vendor: form.vendor,
            apiKey: form.apiKey,
            apiSecret: form.apiSecret,
            sort: form.sort,
            isActive: form.isActive,
            accountId: form.accountId
        }

        if (isEdit.value) {
            await updateTranslationRouteAPI(currentId.value, params)
            $message.success('更新成功')
        } else {
            await createTranslationRouteAPI(params)
            $message.success('添加成功')
        }

        visible.value = false
        emit('success')
    } catch (error) {
        console.error('表单验证失败:', error)
    } finally {
        loading.value = false
    }
}

const handleCancel = () => {
    visible.value = false
    formRef.value?.resetFields()
}

const emit = defineEmits<{
    (e: 'success'): void
}>()

defineExpose({
    open
})
</script>