import request from '../utils/request'

export enum TranslationType {
    TEXT = 'text',
    AUDIO = 'audio',
    VIDEO = 'video',
    IMAGE = 'image',
    DOCUMENT = 'document',
}

export enum VendorType {
    // GOOGLE = 'google',
    BAIDU = 'baidu',
    YOUDAO = 'youdao',
    ALIBABA = 'alibaba',
    // DEEPL = 'deepl'
}

export interface ITranslationRoute {
    id: string
    type: TranslationType
    vendor: VendorType
    apiKey: string
    apiSecret?: string
    isActive: boolean
    sort: number
    accountId: string
    createdAt: string
    updatedAt: string
}

export interface ICreateTranslationRouteParams {
    type: TranslationType
    vendor: VendorType
    apiKey: string
    apiSecret?: string
    isActive?: boolean
    sort?: number
    accountId: string
}

export interface IUpdateTranslationRouteParams {
    type?: TranslationType
    vendor?: VendorType
    apiKey?: string
    apiSecret?: string
    isActive?: boolean
    sort?: number
    accountId?: string
}

export interface IVendorInfo {
    type: VendorType
    name: string
    description: string
    icon: string
    website: string
}

// 创建翻译路由
export function createTranslationRouteAPI(data: ICreateTranslationRouteParams) {
    return request<ITranslationRoute>({
        url: '/client/translation-routes/create',
        method: 'POST',
        data
    })
}

// 更新翻译路由
export function updateTranslationRouteAPI(id: string, data: IUpdateTranslationRouteParams) {
    return request<ITranslationRoute>({
        url: `/client/translation-routes/update/${id}`,
        method: 'PUT',
        data
    })
}

// 获取翻译路由列表
export function getTranslationRouteListAPI() {
    return request<ITranslationRoute[]>({
        url: '/client/translation-routes/list',
        method: 'GET'
    })
}

// 切换翻译路由状态
export function toggleTranslationRouteStatusAPI(id: string) {
    return request<ITranslationRoute>({
        url: `/client/translation-routes/toggle-status/${id}`,
        method: 'PUT'
    })
}

// 删除翻译路由
export function deleteTranslationRouteAPI(id: string) {
    return request<ITranslationRoute>({
        url: `/client/translation-routes/delete/${id}`,
        method: 'DELETE'
    })
}

// 获取翻译供应商列表
export function getVendorsAPI() {
    return request<IVendorInfo[]>({
        url: '/client/translation-routes/vendors',
        method: 'GET'
    })
} 