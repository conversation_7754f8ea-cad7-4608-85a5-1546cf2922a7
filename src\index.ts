import 'reflect-metadata';
import { config } from 'dotenv';
import Koa from 'koa';
import cors from '@koa/cors';
import bodyParser from 'koa-bodyparser';
import { AppDataSource } from './config/database';
import { registerRoutes } from './routes';
import { responseMiddleware } from './middleware/response.middleware';
import { tenantMiddleware } from './middleware/tenant.middleware';

// 加载环境变量
config();

// 创建 Koa 应用实例
const app = new Koa();
const PORT = process.env.PORT || 3000;

// 初始化数据库连接
const initializeDatabase = async () => {
  try {
    await AppDataSource.initialize();
    console.log('数据库连接成功');
  } catch (error) {
    console.error('数据库连接失败:', error);
    process.exit(1);
  }
};

// 应用中间件
app.use(cors());
app.use(bodyParser());
app.use(responseMiddleware);
app.use(tenantMiddleware);

// 注册路由
registerRoutes(app);

// 启动服务器
const startServer = async () => {
  await initializeDatabase();

  app.listen(PORT, () => {
    console.log(`服务器运行在 http://localhost:${PORT}`);
  });
};

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
});

process.on('unhandledRejection', (reason) => {
  console.error('未处理的拒绝:', reason);
});

// 启动应用
startServer().catch((error) => {
  console.error('启动服务器失败:', error);
  process.exit(1);
});
