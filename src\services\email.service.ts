import nodemailer from 'nodemailer';
import { RedisService } from './redis.service';

export class EmailService {
  private transporter: nodemailer.Transporter;
  private redisService: RedisService;
  private readonly EMAIL_PREFIX = 'email:';
  private readonly EMAIL_EXPIRE_TIME = 300; // 5分钟过期

  constructor() {
    this.transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    });
    this.redisService = new RedisService();
  }

  /**
   * 生成6位随机数字验证码
   */
  private generateVerificationCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  /**
   * 发送验证码邮件
   */
  async sendVerificationCode(email: string): Promise<boolean> {
    try {
      const code = this.generateVerificationCode();

      // 存储验证码到Redis
      await this.redisService.set(`${this.EMAIL_PREFIX}${email}`, code, this.EMAIL_EXPIRE_TIME);

      // 发送邮件
      await this.transporter.sendMail({
        from: process.env.SMTP_FROM,
        to: email,
        subject: '注册验证码',
        html: `
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2>验证码</h2>
            <p>您的验证码是：<strong style="color: #1890ff; font-size: 24px;">${code}</strong></p>
            <p>验证码有效期为5分钟，请尽快使用。</p>
            <p>如果这不是您的操作，请忽略此邮件。</p>
          </div>
        `,
      });

      return true;
    } catch (error) {
      console.error('发送验证码邮件失败:', error);
      return false;
    }
  }

  /**
   * 验证邮箱验证码
   */
  async verifyCode(email: string, code: string): Promise<boolean> {
    const key = `${this.EMAIL_PREFIX}${email}`;
    const correctCode = await this.redisService.get(key);

    if (!correctCode) {
      return false;
    }

    // 验证完成后删除验证码
    await this.redisService.del(key);

    return correctCode === code;
  }
}
