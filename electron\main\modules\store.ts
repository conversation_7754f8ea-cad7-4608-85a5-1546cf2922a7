import { TranslateConfig, ProxyConfig, FingerprintConfig } from "../interface";

interface StoreSchema {
    'App-Token'?: string;
    [key: `translate_config_${string}`]: TranslateConfig;
    [key: `proxy_config_${string}`]: ProxyConfig;
    [key: `fingerprint_config_${string}`]: FingerprintConfig;
    [key: `cookies_${string}`]: Electron.Cookie[];
    [key: `localStorage_${string}`]: { [key: string]: string };
}

class StoreManager {
    private store: any = null;

    constructor() {
        this.init();
    }

    private async init() {
        const Store = await import('electron-store');
        this.store = new Store.default<StoreSchema>();
    }

    // Token 相关方法
    public getToken(): string | undefined {
        return this.store?.get('App-Token');
    }

    public setToken(token: string): void {
        this.store?.set('App-Token', token);
    }

    public removeToken(): void {
        this.store?.delete('App-Token');
    }

    // 翻译配置相关方法
    public setTranslateConfig(chatId: string, config: TranslateConfig): void {
        this.store?.set(`translate_config_${chatId}`, config);
    }

    public getTranslateConfig(chatId: string): TranslateConfig | null {
        return this.store?.get(`translate_config_${chatId}`);
    }

    // 代理配置相关方法
    public setProxyConfig(chatId: string, config: ProxyConfig): void {
        this.store?.set(`proxy_config_${chatId}`, config);
    }

    public getProxyConfig(chatId: string): ProxyConfig | null {
        return this.store?.get(`proxy_config_${chatId}`);
    }

    // 指纹配置相关方法
    public setFingerprintConfig(chatId: string, config: FingerprintConfig): void {
        this.store?.set(`fingerprint_config_${chatId}`, config);
    }

    public getFingerprintConfig(chatId: string): FingerprintConfig | null {
        return this.store?.get(`fingerprint_config_${chatId}`);
    }

    // Cookies相关方法
    public setCookies(chatId: string, cookies: Electron.Cookie[]): void {
        this.store?.set(`cookies_${chatId}`, cookies);
    }

    public getCookies(chatId: string): Electron.Cookie[] | null {
        return this.store?.get(`cookies_${chatId}`);
    }

    public removeCookies(chatId: string): void {
        this.store?.delete(`cookies_${chatId}`);
    }

    // localStorage相关方法
    public setLocalStorage(chatId: string, data: { [key: string]: string }): void {
        this.store?.set(`localStorage_${chatId}`, data);
    }

    public getLocalStorage(chatId: string): { [key: string]: string } | null {
        return this.store?.get(`localStorage_${chatId}`);
    }

    public removeLocalStorage(chatId: string): void {
        this.store?.delete(`localStorage_${chatId}`);
    }
}

export const storeManager = new StoreManager(); 