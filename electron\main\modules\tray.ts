import { app, Menu, Tray } from "electron";
import path from 'node:path'
import { PUBLIC } from '../config/config';
let tray;
export function createTray() {
  if (!tray) {
    const trayIconPath = path.join(PUBLIC, 'favicon.ico');
    tray = new Tray(trayIconPath);
    const contextMenu = Menu.buildFromTemplate([
      {
        label: "退出",
        click: () => {
          app.quit();
          process.exit(0);
        },
      },
    ]);
    tray.setContextMenu(contextMenu);
  }
  return tray;
}
