import { getToken } from './utils/auth' // get token from cookie
import router from './router'
import { useUserInfoStore } from './stores/user-info'
import { toLoginWindow, toMainWindow } from './utils/windows'
import { useChatStore } from './stores/chat'
const whiteList = ['/login', '/launch', '/update'] // no redirect whitelist

router.beforeEach(async (to, from, next) => {
    if (whiteList.indexOf(to.path) !== -1) {
        next()
        return;
    }
    const hasToken = await getToken()
    if (hasToken) {
        if (to.path === '/login') {
            // if is logged in, redirect to the home page
            toMainWindow()
        } else {
            const userInfoStore = useUserInfoStore();
            const chatStore = useChatStore();

            // determine whether the user has obtained his permission roles through getInfo
            let userInfo = userInfoStore.userInfo
            if (userInfo) {
                next()
            } else {
                try {
                    // get user info
                    userInfo = await userInfoStore.getUserInfo()
                    if (userInfo) {
                        chatStore.loadChatList();
                        next({ ...to, replace: true })
                    } else {
                        toLoginWindow()
                    }
                } catch (error) {
                    toLoginWindow()
                }
            }
        }
    } else {
        /* has no token*/
        if (whiteList.indexOf(to.path) !== -1) {
            // in the free login whitelist, go directly
            next()
        } else {
            // other pages that do not have permission to access are redirected to the login page.
            toLoginWindow()
        }
    }
})

router.afterEach(() => {

})