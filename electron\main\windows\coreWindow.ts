import { DEV_SERVER_URL, INDEX_HTML } from "../config/config";
import {
    launchWindowOptions,
    loginWindowOptions,
    mainWindowOptions,
    updateWindowOptions
} from './options/index'
import { BaseWindowsManager } from "./baseWindow";
import chatWindowManager from "./chatWindow";
class CoreWindowsManager extends BaseWindowsManager {

    createMainWindow() {
        const winId = 'main';
        const url = DEV_SERVER_URL ? DEV_SERVER_URL : INDEX_HTML;
        const win = this.createWindow(winId, url, mainWindowOptions)
        win.webContents.openDevTools({ mode: 'detach' })
        win.on('move', () => {
            chatWindowManager.setBounds();
        })
        return win;
    }

    createLoginWindow() {
        const winId = 'login';
        const url = DEV_SERVER_URL ? `${DEV_SERVER_URL}/#/login` : `${INDEX_HTML}/#/login`;
        const win = this.createWindow(winId, url, loginWindowOptions);
        return win;
    }

    createLaunchWindow() {
        const winId = 'launch';
        const url = DEV_SERVER_URL ? `${DEV_SERVER_URL}/#/launch` : `${INDEX_HTML}/#/launch`;
        const win = this.createWindow(winId, url, launchWindowOptions);
        return win;
    }

    createUpdateWindow() {
        const winId = 'update';
        const url = DEV_SERVER_URL ? `${DEV_SERVER_URL}/#/update` : `${INDEX_HTML}/#/update`;
        const win = this.createWindow(winId, url, updateWindowOptions);
        return win;
    }

}

export default new CoreWindowsManager();