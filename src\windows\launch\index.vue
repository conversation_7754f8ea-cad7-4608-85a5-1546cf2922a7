<template>
    <div class="w-full h-full flex justify-center items-center -webkit-app-region-drag">启动中...</div>
</template>
<script setup lang="ts">
import { onMounted } from "vue"
import { useUserInfoStore } from "../../stores/user-info";
import { toMainWindow, toLoginWindow, toUpdateWindow } from '../../utils/windows'
const userStore = useUserInfoStore();
onMounted(async () => {
    Promise.all([
        // 获取用户信息
        userStore.getUserInfo()
    ]).then(res => {
        toMainWindow();
    })
})
</script>
<style>
html,
body {
    margin: 0;
    padding: 0;
    background: transparent !important;
}

.-webkit-app-region-drag {
    -webkit-app-region: drag;
}

#app,
.app {
    background-color: transparent;
}
</style>