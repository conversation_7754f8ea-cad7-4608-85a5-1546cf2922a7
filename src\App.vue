<script setup lang="ts">
import SysTitleBar from './components/SysTitleBar/index.vue'
</script>

<template>
  <div class="h-full w-full bg-[#edeff2] shadow rounded-md border overflow-hidden">
    <el-config-provider>
      <div class="flex flex-col h-full">
        <div class="flex-shrink-0">
          <sys-title-bar></sys-title-bar>
        </div>
        <div class="flex-grow overflow-hidden">
          <router-view></router-view>
        </div>
      </div>
    </el-config-provider>
  </div>
</template>
