<template>
    <div class="box-border w-full h-full px-[50px] flex justify-center items-center">
        <el-form ref="loginFormComp" class="w-full" :model="loginForm" :rules="loginRules" :show-label="false">
            <h3 class="text-2xl font-bold text-center mb-10">{{ isRegister ? '注册' : '登录' }}</h3>
            <el-form-item path="email">
                <el-autocomplete v-model="loginForm.email" name="email" type="text" placeholder="请输入邮箱" :input-props="{
                    autocomplete: 'disabled'
                }" :options="inputOptions" @keyup.enter.native="handleSubmit" />
            </el-form-item>
            <el-form-item path="password">
                <el-input v-model="loginForm.password" :type="showPassword ? undefined : 'password'" name="password"
                    placeholder="请输入密码" @keyup.enter.native="handleSubmit" />
                <span class="absolute right-[10px] top-[3px] text-base cursor-pointer select-none">
                    <SvgIcon :name="showPassword ? 'view' : 'hide'" size="18px" @click="toggleShowPassword"></SvgIcon>
                </span>
            </el-form-item>
            <el-form-item path="captchaAnswer" v-if=!isRegister>
                <el-input v-model="loginForm.captchaAnswer" name="captchaAnswer" type="text" placeholder="请输入验证码"
                    @keyup.enter.native="handleSubmit" />
                <img class="absolute right-[2px] top-[2px] h-[calc(100%-4px)] min-w-[80px] box-border align-bottom z-[2] cursor-pointer"
                    :src="code" @click="getCaptcha">
            </el-form-item>
            <el-form-item path="verificationCode" v-if=isRegister>
                <el-input v-model="loginForm.verificationCode" name="verificationCode" type="text"
                    placeholder="请输入邮箱验证码"></el-input>
                <el-button secondary strong type="primary"
                    class="absolute right-[2px] top-[2px] h-[calc(100%-4px)] min-w-[80px] box-border align-bottom z-[2] cursor-pointer"
                    :disabled="isSendEmailCode" @click="sendEmailCode">{{ emailTimeText }}</el-button>
            </el-form-item>
            <el-form-item>
                <div>
                    <el-button quaternary type="primary" style="width:100%;" @click="toggleLogin" link>{{
                        isRegister ? '已有账号登录' :
                            '注册新账号' }}</el-button>
                </div>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" style="width:100%;" :loading="loading" @click.native.prevent="handleSubmit">{{
                    isRegister ?
                        '立即注册' : '立即登录' }}</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>
<script setup lang="ts">
import { ref, nextTick, onMounted, computed } from 'vue';
import SvgIcon from '@/components/SvgIcon/index.vue'
import { getCaptchaAPI, sendEmailCodeAPI, registerAPI } from '../../api/auth'
import { useUserAuthStore } from '../../stores/user-auth'
import { toMainWindow } from '../../utils/windows';
import { $message } from '../../utils/message';
const loginFormComp = ref()
const loginForm = ref({
    email: '<EMAIL>',
    password: '123456',
    captchaAnswer: '',
    verificationCode: '',
    captchaId: '',
});
const loginRules = {
    email: [{
        required: true,
        trigger: 'change',
        message: '请输入邮箱'
    }],
    password: [{
        required: true,
        trigger: 'change',
        message: '请输入密码'
    }],
    captchaAnswer: [{
        required: true,
        trigger: 'change',
        message: '请输入验证码'
    }],
    verificationCode: [{
        required: true,
        trigger: 'change',
        message: '请输入验证码'
    }]
}
let isRegister = ref(false);
let isSendEmailCode = ref(false);
let emailTimer: any = null;
let emailTimeText = ref('点击发送');
let code = ref('')
let loading = ref(false);
let showPassword = ref(false);
const toggleShowPassword = () => {
    showPassword.value = !showPassword.value
}
const toggleLogin = () => {
    isRegister.value = !isRegister.value;
    !isRegister.value && getCaptcha();
    loginFormComp.value.restoreValidation();
}
const inputOptions = computed(() => {
    return ['@gmail.com', '@163.com', '@qq.com'].map((suffix) => {
        const prefix = loginForm.value.email.split('@')[0]
        return {
            label: prefix + suffix,
            value: prefix + suffix
        }
    })
})
const clearEmailTimer = () => {
    clearInterval(emailTimer);
    emailTimer = null;
}
let second = 60;
const createEmailTimer = () => {
    second = 60;
    emailTimer = setInterval(() => {
        if (second > 0) {
            emailTimeText.value = `重新发送${second}秒`;
            second--;
        } else {
            clearEmailTimer()
            isSendEmailCode.value = false;
            emailTimeText.value = '点击发送'
        }
    }, 1000)
}
const sendEmailCode = () => {
    if (isSendEmailCode.value) {
        return;
    }
    isSendEmailCode.value = true;
    sendEmailCodeAPI({
        email: loginForm.value.email
    }).then((_res: any) => {
        clearEmailTimer();
        createEmailTimer();
    }).catch((_err: any) => {
        isSendEmailCode.value = false;
    })
}

const handleSubmit = () => {
    loginFormComp.value.validate((valid: boolean) => {
        if (valid) {
            loading.value = true;
            if (isRegister.value) {
                registerAPI(loginForm.value).then(() => {
                    loading.value = false;
                    $message.success('注册成功');
                    setTimeout(() => {
                        isRegister.value = false;
                    }, 1500)
                }).finally(() => {
                    loading.value = false;
                })
            } else {
                const store = useUserAuthStore();
                store.login(loginForm.value).then(() => {
                    loading.value = false;
                    toMainWindow();
                }).catch(() => {
                    getCaptcha();
                }).finally(() => {
                    loading.value = false;
                })
            }
        } else {
            console.log('error submit!!')
            return false
        }
    })
}

const getCaptcha = () => {
    getCaptchaAPI().then(({ data }) => {
        loginForm.value.captchaAnswer = '';
        loginForm.value.captchaId = data.id;
        code.value = data.dataUrl;
    })
}

onMounted(() => {
    getCaptcha()
})

</script>
<style lang="scss" scoped>
.main {
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    padding: 0 50px;
    display: flex;
    justify-content: center;
    align-items: center;

    .logiel-form {
        width: 100%;
    }

    .title {
        font-size: 26px;
        font-weight: 400;
        margin: 0px auto 40px auto;
        text-align: center;
        font-weight: bold;
    }
}

.show-pwd {
    position: absolute;
    right: 10px;
    top: 3px;
    font-size: 16px;
    cursor: pointer;
    user-select: none;
}

.in-right {
    position: absolute;
    right: 2px;
    top: 2px;
    height: calc(100% - 4px);
    min-width: 80px;
    box-sizing: border-box;
    vertical-align: bottom;
    z-index: 2;
    cursor: pointer;
}
</style>