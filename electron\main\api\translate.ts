import request from "../modules/request";
export enum VendorType {
    GOOGLE = 'google',
    BAIDU = 'baidu',
    YOUDAO = 'youdao',
    DEEPL = 'deepl'
}

export interface ITranslateParams {
    text: string
    from: string
    to: string
    vendor?: string
}

export interface ITranslateResponse {
    text: string
}
// 执行翻译
export function translateAPI(data: ITranslateParams) {
    return request<ITranslateResponse>({
        url: '/client/translation/translate',
        method: 'POST',
        data
    })
}