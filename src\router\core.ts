import Layout from '../layout/index.vue';
import { permssionRoutes } from './perm'

const routes = [
    {
        path: '/',
        component: Layout,
        redirect: "/index",
        children: [
            {
                path: 'index',
                name: 'index',
                component: () => import('@/windows/main/index/index.vue'),
                meta: {
                    // 窗口不能最小化
                    minimizable: true,
                    // 窗口不能进入全屏状态
                    fullscreenable: true,
                    // 窗口不能关闭
                    closeable: true,
                }
            },
            {
                path: 'setting',
                name: 'setting',
                component: () => import('@/windows/main/setting/index.vue'),
                redirect: "/setting/translation-route",
                children: [
                    {
                        path: 'translation-route',
                        name: 'translation-route',
                        component: () => import('@/windows/main/setting/translation-route/index.vue'),
                        meta: {
                            // 窗口不能最小化
                            minimizable: true,
                            // 窗口不能进入全屏状态
                            fullscreenable: true,
                            // 窗口不能关闭
                            closeable: true,
                        }
                    },
                ]
            },
            ...permssionRoutes,
        ]
    },
    {
        path: '/login',
        name: 'login',
        component: () => import('@/windows/login/index.vue'),
        meta: {
            // 窗口不能最小化
            minimizable: true,
            // 窗口不能进入全屏状态
            fullscreenable: false,
            // 窗口不能关闭
            closeable: true,
        }
    },
    {
        path: '/update',
        name: 'update',
        component: () => import('@/windows/update/index.vue'),
        meta: {
            // 隐藏标题栏
            hideTitle: false,
        }
    },
    {
        path: '/launch',
        name: 'launch',
        component: () => import('@/windows/launch/index.vue'),
        meta: {
            // 隐藏标题栏
            hideTitle: false,
        }
    },
];

export { routes };